[2025-06-04 13:07:44] (train_dist.py 145): INFO params: Namespace(train_list='mydatasets/Protocol-train.txt', train_root='mydatasets', val_list='mydatasets/Protocol-train.txt', val_root='mydatasets', imbalanced_sampler=False, use_landmark=False, landmark_base_scale=0.0, landmark_rotate=10, landmark_translation=0.06, landmark_scale=0.06, use_custom_dataset=True, arch=None, input_size=224, num_classes=1000, syncbn=False, ema=False, model_ema_decay=0.99, model_ema_steps=32, lr=0.1, accumulate_step=1, schedule='40,60', cos=False, warmup_steps=0, total_steps=0, optimizer='SGD', workers=4, epochs=1, warmup_epochs=0, start_epoch=0, batch_size=8, momentum=0.9, weight_decay=0.0001, fp16=False, save_freq=1, print_freq=10, pretrain=None, autoresume=False, resume=None, saved_model_dir='saved_models', evaluate=False, test_crop=True, test_five_crop=False, single_center_loss=False, single_center_loss_weight=0.001, protocol=None, live_weight=1.0)
[2025-06-04 13:07:44] (train_dist.py 146): INFO world_size:1, rank:0, local_rank:0
[2025-06-04 13:07:44] (train_dist.py 159): INFO seed_value: 240
[2025-06-04 13:07:44] (train_dist.py 166): INFO train_dataset:22367
[2025-06-04 13:07:44] (train_dist.py 177): INFO train_loader:2795
[2025-06-04 13:07:44] (train_dist.py 181): INFO val_dataset: 22367
[2025-06-04 13:07:44] (train_dist.py 187): INFO val_loader:2796
[2025-06-04 13:07:44] (train_dist.py 191): INFO warmup_steps: 0
[2025-06-04 13:07:44] (train_dist.py 192): INFO total_steps: 2795
[2025-06-04 13:07:44] (train_dist.py 197): INFO => creating model 'None', fp16:False
[2025-06-04 13:08:21] (train_dist.py 145): INFO params: Namespace(train_list='mydatasets/Protocol-train.txt', train_root='mydatasets', val_list='mydatasets/Protocol-train.txt', val_root='mydatasets', imbalanced_sampler=False, use_landmark=False, landmark_base_scale=0.0, landmark_rotate=10, landmark_translation=0.06, landmark_scale=0.06, use_custom_dataset=True, arch='resnet18', input_size=224, num_classes=1000, syncbn=False, ema=False, model_ema_decay=0.99, model_ema_steps=32, lr=0.1, accumulate_step=1, schedule='40,60', cos=False, warmup_steps=0, total_steps=0, optimizer='SGD', workers=4, epochs=1, warmup_epochs=0, start_epoch=0, batch_size=8, momentum=0.9, weight_decay=0.0001, fp16=False, save_freq=1, print_freq=10, pretrain=None, autoresume=False, resume=None, saved_model_dir='saved_models', evaluate=False, test_crop=True, test_five_crop=False, single_center_loss=False, single_center_loss_weight=0.001, protocol=None, live_weight=1.0)
[2025-06-04 13:08:21] (train_dist.py 146): INFO world_size:1, rank:0, local_rank:0
[2025-06-04 13:08:21] (train_dist.py 159): INFO seed_value: 240
[2025-06-04 13:08:21] (train_dist.py 166): INFO train_dataset:22367
[2025-06-04 13:08:21] (train_dist.py 177): INFO train_loader:2795
[2025-06-04 13:08:21] (train_dist.py 181): INFO val_dataset: 22367
[2025-06-04 13:08:21] (train_dist.py 187): INFO val_loader:2796
[2025-06-04 13:08:21] (train_dist.py 191): INFO warmup_steps: 0
[2025-06-04 13:08:21] (train_dist.py 192): INFO total_steps: 2795
[2025-06-04 13:08:21] (train_dist.py 197): INFO => creating model 'resnet18', fp16:False
