#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import torch
import argparse
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
from datasets.utils import get_train_dataset, get_val_dataset

def parse_args():
    parser = argparse.ArgumentParser(description='Test custom dataset loading')
    
    # Dataset parameters
    parser.add_argument('--train-root', type=str, default='Data', help='Root directory of training data')
    parser.add_argument('--train-list', type=str, default='Protocol-train.txt', help='Path to training data list')
    parser.add_argument('--val-root', type=str, default='Data', help='Root directory of validation data')
    parser.add_argument('--val-list', type=str, default='Protocol-val.txt', help='Path to validation data list')
    parser.add_argument('--batch-size', type=int, default=32, help='Batch size for data loading')
    parser.add_argument('--num-workers', type=int, default=4, help='Number of workers for data loading')
    parser.add_argument('--input-size', type=int, default=256, help='Input image size')
    
    # Model parameters
    parser.add_argument('--arch', type=str, default='resnet50', help='Model architecture')
    parser.add_argument('--num-classes', type=int, default=2, help='Number of classes')
    parser.add_argument('--syncbn', action='store_true', help='Use synchronized batch norm')
    
    # Data augmentation
    parser.add_argument('--use-landmark', action='store_true', help='Use facial landmarks for augmentation')
    parser.add_argument('--test-crop', action='store_true', help='Use center crop for testing')
    parser.add_argument('--landmark-base-scale', type=float, default=0.0, help='Base scale for landmark augmentation')
    parser.add_argument('--landmark-rotate', type=int, default=10, help='Rotation range for landmark augmentation')
    parser.add_argument('--landmark-translation', type=float, default=0.06, help='Translation range for landmark augmentation')
    parser.add_argument('--landmark-scale', type=float, default=0.06, help='Scale range for landmark augmentation')
    
    # Other parameters
    parser.add_argument('--use-custom-dataset', action='store_true', help='Use custom dataset')
    parser.add_argument('--fp16', action='store_true', help='Use mixed precision training')
    parser.add_argument('--ema', action='store_true', help='Use model EMA')
    parser.add_argument('--model-ema-decay', type=float, default=0.9998, help='Model EMA decay')
    parser.add_argument('--model-ema-steps', type=int, default=32, help='Model EMA steps')
    
    return parser.parse_args()

def visualize_batch(images, labels, title):
    """Visualize a batch of images with their labels"""
    try:
        # Handle case where images is a list of tensors
        if isinstance(images, list) and len(images) > 0 and torch.is_tensor(images[0]):
            images = torch.stack(images, dim=0)
        
        # Handle single tensor input
        if torch.is_tensor(images):
            # Convert to numpy and handle device
            images = images.detach().cpu().numpy()
            
            # Handle different tensor shapes
            if len(images.shape) == 3:  # Could be (H, C, W) or (C, H, W)
                if images.shape[0] == 3:  # (C, H, W) -> (H, W, C)
                    images = np.transpose(images, (1, 2, 0))
                elif images.shape[1] == 3:  # (H, C, W) -> (H, W, C)
                    images = np.transpose(images, (0, 2, 1))
                # If it's (H, W, C), leave it as is
            
            # Add batch dimension if needed
            if len(images.shape) == 3:
                images = images[None, ...]  # (H, W, C) -> (1, H, W, C)
        
        # Handle labels
        if torch.is_tensor(labels):
            labels = labels.detach().cpu().numpy()
        
        # Get batch size
        batch_size = images.shape[0]
        
        # Create subplots
        fig, axes = plt.subplots(1, min(4, batch_size), figsize=(15, 5))
        if batch_size == 1:
            axes = [axes]
        
        for i in range(min(4, batch_size)):  # Show up to 4 images
            img = images[i]
            label = labels[i] if i < len(labels) else "N/A"
            
            # Ensure image is in (H, W, C) format
            if len(img.shape) == 3:
                if img.shape[0] == 3:  # (C, H, W) -> (H, W, C)
                    img = np.transpose(img, (1, 2, 0))
                elif img.shape[1] == 3:  # (H, C, W) -> (H, W, C)
                    img = np.transpose(img, (0, 2, 1))
            
            # Undo normalization if needed
            if img.max() <= 1.0:
                mean = np.array([0.485, 0.456, 0.406])
                std = np.array([0.229, 0.224, 0.225])
                # Apply normalization per channel
                if len(img.shape) == 3:
                    img = img * std + mean
                else:
                    # Handle case where image is already (H, W, C)
                    img = img * std.reshape(1, 1, 3) + mean.reshape(1, 1, 3)
                img = np.clip(img, 0, 1)
            
            # Convert to uint8 for display
            img = (img * 255).astype(np.uint8)
            
            axes[i].imshow(img)
            axes[i].set_title(f'Label: {label}')
            axes[i].axis('off')
        
        plt.suptitle(title)
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"Error in visualization: {e}")
        print(f"Image shape: {images.shape if 'images' in locals() else 'N/A'}")
        print(f"Label shape: {labels.shape if hasattr(labels, 'shape') else 'N/A'}")
        import traceback
        traceback.print_exc()

def test_custom_dataset(args):
    """Test the custom dataset with visualization"""
    # Set up dummy args for dataset
    class Args:
        def __init__(self):
            # Basic dataset parameters
            self.protocol = ''  # This will be overridden by use_custom_dataset
            self.use_custom_dataset = True
            self.test_crop = False
            
            # Landmark parameters
            self.use_landmark = False
            self.landmark_base_scale = 0.0
            self.landmark_rotate = 10
            self.landmark_translation = 0.06
            self.landmark_scale = 0.06
            
            # Model parameters
            self.arch = 'resnet50'
            self.input_size = 224
            self.num_classes = 2  # Binary classification: real(0) vs fake(1)
            self.syncbn = False
            
            # Training parameters
            self.epochs = 10
            self.batch_size = 8
            self.lr = 0.001
            self.momentum = 0.9
            self.weight_decay = 1e-4
            self.workers = 4
            
            # Paths
            self.saved_model_dir = './output'
            
            # Other parameters
            self.fp16 = False
            self.ema = False
            self.model_ema_decay = 0.9998
            self.model_ema_steps = 32
            self.start_epoch = 0
            self.evaluate = False
            self.pretrain = None
            self.resume = None
            self.autoresume = False
            self.seed = 42
            self.distributed = False
    
    dummy_args = Args()
    
    print("Loading training dataset...")
    try:
        train_dataset = get_train_dataset(
            args.train_root, 
            args.train_list, 
            input_size=args.input_size,
            args=args
        )
        print(f"Training dataset size: {len(train_dataset)}")
        
        # Print sample items
        print("\nSample training items:")
        for i in range(min(3, len(train_dataset))):
            try:
                item = train_dataset[i]
                if isinstance(item, tuple) and len(item) >= 2:
                    print(f"Item {i}: Image shape: {item[0].shape if hasattr(item[0], 'shape') else 'N/A'}, "
                          f"Label: {item[1]}")
                else:
                    print(f"Item {i}: Unexpected format: {type(item)}")
            except Exception as e:
                print(f"Error loading item {i}: {str(e)}")
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=min(args.batch_size, 4),  # Use smaller batch size for testing
            shuffle=True,
            num_workers=min(2, args.num_workers),  # Use fewer workers for debugging
            pin_memory=True,
            drop_last=True  # Avoid issues with last incomplete batch
        )
        
        print("\nTesting training data loading...")
        try:
            # Get one batch
            batch = next(iter(train_loader))
            if isinstance(batch, (list, tuple)) and len(batch) >= 2:
                images, labels = batch[:2]  # Unpack first two elements as image and label
                print(f"Batch type: {type(batch)}")
                print(f"Batch length: {len(batch)}")
                print(f"Images type: {type(images)}")
                print(f"Images shape: {images.shape if hasattr(images, 'shape') else 'N/A'}")
                print(f"Labels: {labels}")
                
                # Visualize samples
                print("\nVisualizing training samples...")
                try:
                    visualize_batch(images, labels, title="Training Samples")
                except Exception as e:
                    print(f"Error during visualization: {str(e)}")
            else:
                print(f"Unexpected batch format: {type(batch)}")
                if hasattr(batch, '__len__'):
                    print(f"Batch length: {len(batch)}")
                print(f"Batch content sample: {str(batch)[:200]}...")
                
        except Exception as e:
            print(f"Error during training data loading: {str(e)}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"Error initializing training dataset: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*50 + "\n")
    
    print("Loading validation dataset...")
    try:
        val_dataset = get_val_dataset(
            args.val_root,
            args.val_list,
            input_size=args.input_size,
            args=args
        )
        print(f"Validation dataset size: {len(val_dataset)}")
        
        # Print sample items
        print("\nSample validation items:")
        for i in range(min(3, len(val_dataset))):
            try:
                item = val_dataset[i]
                if isinstance(item, tuple) and len(item) >= 2:
                    print(f"Item {i}: Image shape: {item[0].shape if hasattr(item[0], 'shape') else 'N/A'}, "
                          f"Label: {item[1]}")
                else:
                    print(f"Item {i}: Unexpected format: {type(item)}")
            except Exception as e:
                print(f"Error loading item {i}: {str(e)}")
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=min(args.batch_size, 4),  # Use smaller batch size for testing
            shuffle=False,
            num_workers=min(2, args.num_workers),  # Use fewer workers for debugging
            pin_memory=True,
            drop_last=True  # Avoid issues with last incomplete batch
        )
        
        print("\nTesting validation data loading...")
        try:
            # Get one batch
            batch = next(iter(val_loader))
            if isinstance(batch, (list, tuple)) and len(batch) >= 2:
                images, labels = batch[:2]  # Unpack first two elements as image and label
                print(f"Batch type: {type(batch)}")
                print(f"Batch length: {len(batch)}")
                print(f"Images type: {type(images)}")
                print(f"Images shape: {images.shape if hasattr(images, 'shape') else 'N/A'}")
                print(f"Labels: {labels}")
                
                # Visualize samples
                print("\nVisualizing validation samples...")
                try:
                    visualize_batch(images, labels, title="Validation Samples")
                except Exception as e:
                    print(f"Error during visualization: {str(e)}")
            else:
                print(f"Unexpected batch format: {type(batch)}")
                if hasattr(batch, '__len__'):
                    print(f"Batch length: {len(batch)}")
                print(f"Batch content sample: {str(batch)[:200]}...")
                
        except Exception as e:
            print(f"Error during validation data loading: {str(e)}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"Error initializing validation dataset: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    args = parse_args()
    test_custom_dataset(args)
