import os
import cv2
import random
import numpy as np
import torch
from torch.utils.data import Dataset
from torchvision import transforms
from PIL import Image
import albumentations as A
from albumentations.pytorch import ToTensorV2
import traceback
from typing import Optional, Tuple, Union, Dict, Any

class MyCustomDataset(Dataset):
    def __init__(self, basedir, data_list, transforms1=None, transforms2=None, is_train=True, return_path=False):
        """
        Custom dataset for loading face anti-spoofing data
        
        Args:
            basedir (str): Base directory containing the data
            data_list (str): Path to the protocol file
            transforms1: First set of transformations
            transforms2: Second set of transformations (e.g., albumentations)
            is_train (bool): Whether this is training data
            return_path (bool): Whether to return the image path along with the image and label
        """
        self.base_path = basedir
        self.transforms1 = transforms1
        self.transforms2 = transforms2
        self.return_path = return_path
        self.is_train = is_train
        self.items = []

        # Read the protocol file
        with open(data_list, 'r') as f:
            lines = f.readlines()
            for line in lines:
                parts = line.strip().split()
                if len(parts) == 2:  # Should be [image_path, label]
                    img_path = parts[0]
                    label_str = parts[1]
                    
                    # Convert label to binary: 0 for real, 1 for any type of spoofing
                    label = 0 if label_str == '0' else 1
                    
                    # For training data, we can use the full label for fine-grained classification
                    # For validation, we use binary labels
                    if not is_train:
                        self.items.append((img_path, label))
                    else:
                        self.items.append((img_path, label, label_str))  # Keep original label for potential use

    def __len__(self):
        return len(self.items)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get an item from the dataset
        
        Args:
            idx: Index of the item to retrieve
            
        Returns:
            Tuple containing:
                - Transformed image tensor
                - Label tensor
                - (Optional) Image path if return_path is True
        """
        item = self.items[idx]
        
        # Handle different data formats
        if len(item) == 2:  # (img_path, label)
            img_path, label = item
        elif len(item) == 3:  # (img_path, label, _) - some datasets include extra info
            img_path, label, _ = item
        else:
            raise ValueError(f"Unexpected item format: {item}")
            
        full_img_path = os.path.join(self.base_path, img_path)
        
        max_retries = 10  # Maximum number of retries for loading an image
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # Read image
                image = cv2.imread(full_img_path)
                if image is None:
                    raise ValueError(f"Failed to load image: {full_img_path}")
                
                # Store original shape for debugging
                original_shape = image.shape
                
                # Convert label to tensor
                label_tensor = torch.tensor(label, dtype=torch.float32)
                
                # Apply first set of transformations (cv2_transform)
                if self.transforms1 is not None:
                    if hasattr(self.transforms1, '__call__'):
                        image = self.transforms1(image)
                
                # Ensure image is in correct format for albumentations
                if len(image.shape) == 2:  # Grayscale
                    image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
                elif image.shape[2] == 1:  # Single channel
                    image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
                elif image.shape[2] == 4:  # RGBA
                    image = cv2.cvtColor(image, cv2.COLOR_RGBA2RGB)
                elif image.shape[2] == 3:  # BGR to RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                
                # Apply second set of transformations (albumentations)
                if self.transforms2 is not None:
                    # Ensure image is numpy array with correct dtype
                    if isinstance(image, Image.Image):
                        image = np.array(image)
                    
                    # Convert to uint8 if needed (albumentations expects uint8 for most transforms)
                    if image.dtype != np.uint8:
                        if image.max() <= 1.0:
                            image = (image * 255).astype(np.uint8)
                    
                    try:
                        # Check if this is an albumentations transform
                        if hasattr(self.transforms2, 'add_targets') or hasattr(self.transforms2, 'image'):
                            # For albumentations, we need to pass the image as a named argument
                            transformed = self.transforms2(image=image)
                            if isinstance(transformed, dict):
                                image = transformed['image']
                            else:
                                image = transformed
                        else:
                            # For torchvision transforms, apply directly
                            image = self.transforms2(image)
                    except Exception as e:
                        print(f"Error in transforms2: {e}")
                        print(f"Transform type: {type(self.transforms2)}")
                        print(f"Image shape: {image.shape}, dtype: {image.dtype}")
                        if hasattr(image, 'min') and hasattr(image, 'max'):
                            print(f"Image range: {image.min()} to {image.max()}")
                        raise
                
                # Convert to tensor if not already
                if not isinstance(image, torch.Tensor):
                    image = torch.from_numpy(image).float()
                
                # Ensure proper shape (C, H, W)
                if len(image.shape) == 2:  # H, W -> 1, H, W
                    image = image.unsqueeze(0)
                elif len(image.shape) == 3:
                    if image.shape[2] == 3:  # H, W, C -> C, H, W
                        image = image.permute(2, 0, 1)
                    elif image.shape[0] == 3:  # Already C, H, W
                        pass
                    else:
                        raise ValueError(f"Unexpected image shape: {image.shape}")
                
                # Normalize if not already done by transforms
                if image.max() > 1.0:
                    image = image.float() / 255.0
                
                if self.return_path:
                    return image, label_tensor, full_img_path
                return image, label_tensor
                
            except Exception as e:
                retry_count += 1
                print(f"Error loading {full_img_path} (attempt {retry_count}/{max_retries}): {str(e)}")
                print(f"Original shape: {original_shape if 'original_shape' in locals() else 'N/A'}")
                print(f"Current shape: {image.shape if 'image' in locals() else 'N/A'}")
                print(traceback.format_exc())
                
                # If we've tried too many times, raise the exception
                if retry_count >= max_retries:
                    print(f"Failed to load {full_img_path} after {max_retries} attempts")
                    # If in training mode, return a random sample instead of failing
                    if self.is_train:
                        idx = random.randrange(0, len(self.items))
                        continue
                    else:
                        raise
                
                # Try loading a different random image
                idx = random.randrange(0, len(self.items))
                img_path, label = self.items[idx]
                full_img_path = os.path.join(self.base_path, img_path)
        
        # If we get here, all retries failed
        raise RuntimeError(f"Failed to load any valid image after {max_retries} attempts")
